package device

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"regexp"
	"strconv"
	"strings"
	"syscall"
	"unsafe"

	"golang.org/x/sys/unix"
)

// DetectDevices discovers block devices on a Linux system.
func DetectDevices() ([]Info, error) {
	var devices []Info

	partitions, err := readProcPartitions()
	if err != nil {
		return nil, fmt.Errorf("failed to read /proc/partitions: %w", err)
	}

	for _, p := range partitions {
		// We only want to list whole devices, not partitions (e.g., sda, not sda1)
		if !isWholeDevice(p.Name) {
			continue
		}

		devPath := "/dev/" + p.Name
		device := Info{
			Path: devPath,
			Name: p.Name,
			Size: p.Size * 1024, // /proc/partitions is in KB
		}
		enrichDeviceInfo(&device)
		devices = append(devices, device)
	}

	return devices, nil
}

// GetPartitions returns all partitions for a given device
func GetPartitions(deviceName string) ([]Info, error) {
	var partitions []Info

	allPartitions, err := readProcPartitions()
	if err != nil {
		return nil, fmt.Errorf("failed to read /proc/partitions: %w", err)
	}

	for _, p := range allPartitions {
		// Check if this partition belongs to the specified device
		if strings.HasPrefix(p.Name, deviceName) && p.Name != deviceName {
			devPath := "/dev/" + p.Name
			partition := Info{
				Path: devPath,
				Name: p.Name,
				Size: p.Size * 1024, // /proc/partitions is in KB
			}
			enrichPartitionInfo(&partition)
			partitions = append(partitions, partition)
		}
	}

	return partitions, nil
}

// GetDetailedDeviceInfo returns detailed information about a device including filesystem info
func GetDetailedDeviceInfo(devicePath string) (map[string]string, error) {
	info := make(map[string]string)

	// Extract device name from path
	parts := strings.Split(devicePath, "/")
	deviceName := parts[len(parts)-1]

	// Get basic device info
	sysPath := "/sys/block/" + deviceName

	if model, err := os.ReadFile(sysPath + "/device/model"); err == nil {
		info["Model"] = strings.TrimSpace(string(model))
	}
	if vendor, err := os.ReadFile(sysPath + "/device/vendor"); err == nil {
		info["Vendor"] = strings.TrimSpace(string(vendor))
	}
	if serial, err := os.ReadFile(sysPath + "/device/serial"); err == nil {
		info["Serial"] = strings.TrimSpace(string(serial))
	}
	if rotational, err := os.ReadFile(sysPath + "/queue/rotational"); err == nil {
		if strings.TrimSpace(string(rotational)) == "0" {
			info["Type"] = "SSD/NVMe"
		} else {
			info["Type"] = "HDD"
		}
	}
	if scheduler, err := os.ReadFile(sysPath + "/queue/scheduler"); err == nil {
		info["Scheduler"] = strings.TrimSpace(string(scheduler))
	}

	// Get size
	if size, err := GetDeviceSize(devicePath); err == nil {
		info["Size"] = fmt.Sprintf("%d bytes", size)
	}

	// Get block size
	info["Block Size"] = fmt.Sprintf("%d bytes", getBlockSize(devicePath))

	return info, nil
}

func enrichPartitionInfo(partition *Info) {
	// For partitions, we can get some basic info but not as much as whole devices
	partition.BlockSize = getBlockSize(partition.Path)
}

// isWholeDevice checks if a device name from /proc/partitions represents a whole disk.
// This is a more reliable heuristic. It assumes anything ending in a digit
// is a partition, UNLESS it's a known device pattern like 'nvmeXnY' or 'mmcblkX'.
func isWholeDevice(name string) bool {
	// NVMe (e.g., nvme0n1) and MMC (e.g., mmcblk0) devices are whole disks, even with trailing numbers.
	if matched, _ := regexp.MatchString(`^(nvme\d+n\d+|mmcblk\d+)$`, name); matched {
		return true
	}
	// A common heuristic: if it ends with a number, it's likely a partition (e.g., sda1, sdb2).
	if matched, _ := regexp.MatchString(`.*\d$`, name); matched {
		return false
	}
	// Otherwise, it's likely a whole disk (e.g., sda, sdb).
	return true
}

type procPartition struct {
	Name string
	Size int64 // Size in KB
}

func readProcPartitions() ([]procPartition, error) {
	file, err := os.Open("/proc/partitions")
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var partitions []procPartition
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		fields := strings.Fields(line)
		if len(fields) < 4 {
			continue
		}
		// Skip header
		if _, err := strconv.Atoi(fields[0]); err != nil {
			continue
		}

		blocks, _ := strconv.ParseInt(fields[2], 10, 64)
		name := fields[3]

		partitions = append(partitions, procPartition{Name: name, Size: blocks})
	}

	return partitions, scanner.Err()
}

func enrichDeviceInfo(device *Info) {
	// Get device properties from /sys/block
	sysPath := "/sys/block/" + device.Name
	if model, err := os.ReadFile(sysPath + "/device/model"); err == nil {
		device.Model = strings.TrimSpace(string(model))
	}
	if vendor, err := os.ReadFile(sysPath + "/device/vendor"); err == nil {
		device.Vendor = strings.TrimSpace(string(vendor))
	}
	device.BlockSize = getBlockSize(device.Path)
}

func getBlockSize(devicePath string) int64 {
	file, err := os.Open(devicePath)
	if err != nil {
		return 512 // Default
	}
	defer file.Close()

	fd := int(file.Fd())
	blockSize, err := unix.IoctlGetInt(fd, unix.BLKSSZGET)
	if err != nil {
		return 512 // Default if ioctl fails
	}
	return int64(blockSize)
}

// GetDeviceSize uses an ioctl to get the precise size of a block device.
func GetDeviceSize(devicePath string) (int64, error) {
	file, err := os.Open(devicePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	// For regular files, just use Stat()
	stat, err := file.Stat()
	if err != nil {
		return 0, err
	}
	if !stat.IsDir() && (stat.Mode()&os.ModeDevice == 0) {
		return stat.Size(), nil
	}

	// For block devices, use the ioctl
	fd := int(file.Fd())
	var size uint64
	_, _, errno := unix.Syscall(unix.SYS_IOCTL, uintptr(fd), unix.BLKGETSIZE64, uintptr(unsafe.Pointer(&size)))
	if errno != 0 {
		return 0, fmt.Errorf("ioctl(BLKGETSIZE64) failed with errno: %d", errno)
	}

	return int64(size), nil
}

// CopyFileRange uses the kernel's zero-copy functionality for maximum speed.
func CopyFileRange(in, out *os.File, totalSize int64) (int64, error) {
	remaining := totalSize
	var written int64

	for remaining > 0 {
		n, err := unix.CopyFileRange(int(in.Fd()), nil, int(out.Fd()), nil, int(remaining), 0)
		if n > 0 {
			written += int64(n)
			remaining -= int64(n)
		}
		if err != nil {
			if err == syscall.ENOSYS {
				return written, err
			}
			return written, fmt.Errorf("copy_file_range failed: %w", err)
		}
		if n == 0 {
			break
		}
	}
	if written != totalSize {
		return written, io.ErrShortWrite
	}
	return written, nil
}
