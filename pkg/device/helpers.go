package device

import (
	"fmt"
	"os"
	"strings"
)

// SuggestBlockSize provides a sensible default block size based on device type.
func SuggestBlockSize(path string) (int, error) {
    // For regular files, use 4MB for optimal performance
    if !strings.HasPrefix(path, "/dev/") {
        return 4 * 1024 * 1024, nil
    }
    
    // Extract device name
    parts := strings.Split(path, "/")
    devName := parts[len(parts)-1]

    // Check if it's an NVMe device (benefits from larger blocks)
    if strings.HasPrefix(devName, "nvme") {
        return 8 * 1024 * 1024, nil
    }
    
    // Check if it's an SSD
    rotationalPath := fmt.Sprintf("/sys/block/%s/queue/rotational", devName)
    if data, err := os.ReadFile(rotationalPath); err == nil {
        if strings.TrimSpace(string(data)) == "0" {
            return 4 * 1024 * 1024, nil // SSD
        }
    }
    
    // Default for HDDs and unknown devices
    return 1 * 1024 * 1024, nil
}
