package copier

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestTempFileCreation(t *testing.T) {
	// Test temporary file creation and cleanup
	tempDir := t.TempDir()

	tests := []struct {
		name           string
		finalOutput    string
		shouldUseTmp   bool
		expectedSuffix string
	}{
		{"With verification", filepath.Join(tempDir, "test1.txt"), true, ".tmp.bella"},
		{"With compression", filepath.Join(tempDir, "test2.txt"), true, ".tmp.bella"},
		{"Simple copy", filepath.Join(tempDir, "test3.txt"), false, ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := NewConfig()
			cfg.Output = tt.finalOutput

			if tt.name == "With verification" {
				cfg.Verify = true
			} else if tt.name == "With compression" {
				cfg.Compression = "compress"
			}

			var tempOutput string
			var needsCleanup bool

			// Simulate the temp file logic from copyFileMultiStage
			if cfg.Verify || cfg.Compression == "compress" {
				tempOutput = tt.finalOutput + ".tmp.bella"
				needsCleanup = true
			} else {
				tempOutput = tt.finalOutput
				needsCleanup = false
			}

			if tt.shouldUseTmp {
				if !strings.HasSuffix(tempOutput, tt.expectedSuffix) {
					t.Errorf("Expected temp file to have suffix %s, got %s", tt.expectedSuffix, tempOutput)
				}
				if !needsCleanup {
					t.Error("Expected needsCleanup to be true for temp file")
				}
			} else {
				if tempOutput != tt.finalOutput {
					t.Errorf("Expected temp output to equal final output, got %s vs %s", tempOutput, tt.finalOutput)
				}
				if needsCleanup {
					t.Error("Expected needsCleanup to be false for direct output")
				}
			}
		})
	}
}

func TestFileOverwritePrompt(t *testing.T) {
	// Test file overwrite/append prompt functionality
	tempDir := t.TempDir()
	existingFile := filepath.Join(tempDir, "existing.txt")

	// Create an existing file
	err := os.WriteFile(existingFile, []byte("existing content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name       string
		isUI       bool
		expected   string
		shouldFail bool
	}{
		{"UI mode", true, "overwrite", false},
		// Skip CLI mode test as it requires interactive input
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test the prompt function
			action, err := promptForFileAction(existingFile, tt.isUI)

			if tt.shouldFail && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.shouldFail && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
			if action != tt.expected {
				t.Errorf("Expected action %s, got %s", tt.expected, action)
			}
		})
	}
}

func TestAppendModeFileOperations(t *testing.T) {
	// Test append mode functionality
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "append_test.txt")

	// Create initial file content
	initialContent := "initial content\n"
	err := os.WriteFile(testFile, []byte(initialContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Test append mode
	appendContent := "appended content\n"

	// Open file in append mode
	file, err := os.OpenFile(testFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		t.Fatalf("Failed to open file in append mode: %v", err)
	}

	_, err = file.WriteString(appendContent)
	if err != nil {
		t.Fatalf("Failed to write to file: %v", err)
	}
	file.Close()

	// Verify content was appended
	finalContent, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read final content: %v", err)
	}

	expected := initialContent + appendContent
	if string(finalContent) != expected {
		t.Errorf("Expected content %q, got %q", expected, string(finalContent))
	}
}

func TestOverwriteModeFileOperations(t *testing.T) {
	// Test overwrite mode functionality
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "overwrite_test.txt")

	// Create initial file content
	initialContent := "initial content\n"
	err := os.WriteFile(testFile, []byte(initialContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Test overwrite mode
	newContent := "new content\n"

	// Open file in overwrite mode
	file, err := os.OpenFile(testFile, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0666)
	if err != nil {
		t.Fatalf("Failed to open file in overwrite mode: %v", err)
	}

	_, err = file.WriteString(newContent)
	if err != nil {
		t.Fatalf("Failed to write to file: %v", err)
	}
	file.Close()

	// Verify content was overwritten
	finalContent, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read final content: %v", err)
	}

	if string(finalContent) != newContent {
		t.Errorf("Expected content %q, got %q", newContent, string(finalContent))
	}
}

func TestTempFileCleanup(t *testing.T) {
	// Test temporary file cleanup on success and failure
	tempDir := t.TempDir()

	tests := []struct {
		name          string
		simulateError bool
		shouldCleanup bool
	}{
		{"Success case", false, true},
		{"Error case", true, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tempFile := filepath.Join(tempDir, "temp_"+tt.name+".tmp.bella")

			// Create a temporary file
			err := os.WriteFile(tempFile, []byte("temp content"), 0644)
			if err != nil {
				t.Fatalf("Failed to create temp file: %v", err)
			}

			// Verify file exists
			if _, err := os.Stat(tempFile); os.IsNotExist(err) {
				t.Fatal("Temp file should exist")
			}

			// Simulate cleanup logic
			if tt.shouldCleanup {
				if tt.simulateError {
					// In error case, we should still clean up
					os.Remove(tempFile)
				} else {
					// In success case, we should also clean up after moving/processing
					os.Remove(tempFile)
				}
			}

			// Verify file is cleaned up
			if _, err := os.Stat(tempFile); !os.IsNotExist(err) {
				t.Error("Temp file should have been cleaned up")
			}
		})
	}
}

func TestFileOperationFlags(t *testing.T) {
	// Test different file operation flags
	tests := []struct {
		name     string
		append   bool
		expected int
	}{
		{"Append mode", true, os.O_CREATE | os.O_WRONLY | os.O_APPEND},
		{"Overwrite mode", false, os.O_CREATE | os.O_WRONLY | os.O_TRUNC},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var openFlags int
			openFlags = os.O_CREATE | os.O_WRONLY
			if tt.append {
				openFlags |= os.O_APPEND
			} else {
				openFlags |= os.O_TRUNC
			}

			if openFlags != tt.expected {
				t.Errorf("Expected flags %d, got %d", tt.expected, openFlags)
			}
		})
	}
}

func TestMultiStageConditions(t *testing.T) {
	// Test conditions that trigger multi-stage processing
	tests := []struct {
		name        string
		operation   OperationType
		verify      bool
		compression string
		input       string
		expected    bool
	}{
		{"No multi-stage", OpCopy, false, "none", "test.txt", false},
		{"With verification", OpCopy, true, "none", "test.txt", true},
		{"With compression", OpCopy, false, "compress", "test.txt", true},
		{"Both verify and compress", OpCopy, true, "compress", "test.txt", true},
		{"Decompress only", OpCopy, false, "decompress", "test.txt", false},
		{"Auto compress non-gz", OpCopy, false, "auto", "test.txt", true},
		{"Auto decompress gz", OpCopy, false, "auto", "test.txt.gz", false},
		{"Non-copy operation", OpWipe, true, "compress", "test.txt", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := NewConfig()
			cfg.Operation = tt.operation
			cfg.Verify = tt.verify
			cfg.Compression = tt.compression
			cfg.Input = tt.input

			// Test the ShouldUseMultiStage logic
			result := cfg.ShouldUseMultiStage()
			if result != tt.expected {
				t.Errorf("Expected ShouldUseMultiStage %v, got %v", tt.expected, result)
			}
		})
	}
}
