package copier

import (
	"bufio"
	"bytes"
	"compress/gzip"
	"crypto/rand"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"syscall"

	"bella/pkg/device"
	"bella/pkg/progress"
)

// promptForFileAction prompts the user when a file already exists.
// It now returns a new "verify" action.
func promptForFileAction(outputPath string, isUI bool) (string, error) {
	if isUI {
		// UI mode has its own dialog, but we can default to overwrite for its logic path.
		return "overwrite", nil
	}

	fmt.Printf("File '%s' already exists.\n", outputPath)
	fmt.Print("Choose action: (o)verwrite, (a)ppend, (v)erify only, (c)ancel [o/a/v/c]: ")

	reader := bufio.NewReader(os.Stdin)
	response, err := reader.ReadString('\n')
	if err != nil {
		return "", fmt.Errorf("failed to read user input: %w", err)
	}

	response = strings.ToLower(strings.TrimSpace(response))
	switch response {
	case "o", "overwrite":
		return "overwrite", nil
	case "a", "append":
		return "append", nil
	case "v", "verify":
		return "verify", nil
	case "c", "cancel":
		return "cancel", nil
	default:
		return "cancel", fmt.Errorf("invalid choice: %s", response)
	}
}

func Execute(cfg *Config) error {
	if cfg.DryRun {
		fmt.Printf("Dry run: would perform operation with config: %+v\n", cfg)
		return nil
	}
	switch cfg.Operation {
	case OpCopy:
		if cfg.isRecursive {
			return copyDirectory(cfg)
		}
		return copyFile(cfg)
	case OpWipe:
		return doWipe(cfg)
	case OpVerify:
		return doVerify(cfg)
	default:
		return fmt.Errorf("no operation specified or recognized")
	}
}

// copyFile is now the central point for overwrite/append checks.
func copyFile(cfg *Config) error {
	// Check for existing final output file BEFORE any temp files are made.
	if _, err := os.Stat(cfg.Output); err == nil && !cfg.Append {
		action, err := promptForFileAction(cfg.Output, cfg.ProgressChan != nil)
		if err != nil {
			return err
		}
		switch action {
		case "cancel":
			return fmt.Errorf("operation cancelled by user")
		case "append":
			cfg.Append = true // Set append mode if chosen
		case "verify":
			// Switch the entire operation to a verify-only task
			cfg.Operation = OpVerify
			return doVerify(cfg)
		case "overwrite":
			// Proceed with the copy
		}
	}

	if cfg.ShouldUseMultiStage() {
		return copyFileMultiStage(cfg)
	}
	return copyFileSingleStage(cfg)
}

func copyFileMultiStage(cfg *Config) error {
	finalOutput := cfg.Output
	tempOutput := finalOutput + ".tmp.bella"
	failedOutput := finalOutput + ".FAILED"

	// Clean up any previous failed attempts before starting.
	os.Remove(failedOutput)

	// Create a config for the raw copy stage.
	rawCfg := *cfg
	rawCfg.Compression = "none"
	rawCfg.Verify = false
	rawCfg.Output = tempOutput

	err := copyFileSingleStage(&rawCfg)
	if err != nil {
		// If the very first stage fails, there's nothing to save.
		os.Remove(tempOutput)
		return fmt.Errorf("stage 1 (copy) failed: %w", err)
	}

	// Ensure all data is written to disk before verification
	if tempFile, err := os.OpenFile(tempOutput, os.O_WRONLY, 0); err == nil {
		tempFile.Sync()
		tempFile.Close()
	}

	// Verification stage
	if cfg.Verify {
		verifyCfg := *cfg
		verifyCfg.Operation = OpVerify
		verifyCfg.Input = cfg.Input
		verifyCfg.Output = tempOutput // Verify against the temp file
		verifyCfg.Compression = "none"

		err = doVerify(&verifyCfg)
		if err != nil {
			// On failure, rename the temp file to .FAILED and return.
			os.Rename(tempOutput, failedOutput)
			return fmt.Errorf("stage 2 (verification) failed: %w. The incomplete file has been saved as %s", err, failedOutput)
		}
	}

	// Compression stage
	if cfg.Compression == "compress" {
		err = compressFile(tempOutput, finalOutput, cfg)
		if err != nil {
			// On failure, rename the temp file to .FAILED and return.
			os.Rename(tempOutput, failedOutput)
			return fmt.Errorf("stage 3 (compression) failed: %w. The incomplete file has been saved as %s", err, failedOutput)
		}
		// On success, the temp file is no longer needed.
		os.Remove(tempOutput)
	} else {
		// If we are not compressing, the final step is to rename the temp file.
		if err := os.Rename(tempOutput, finalOutput); err != nil {
			return fmt.Errorf("failed to rename temporary file to final output: %w", err)
		}
	}

	return nil
}

func copyFileSingleStage(cfg *Config) error {
	in, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open input '%s': %w", cfg.Input, err)
	}
	defer in.Close()

	openFlags := os.O_CREATE | os.O_WRONLY
	if cfg.Append {
		openFlags |= os.O_APPEND
	} else {
		openFlags |= os.O_TRUNC
	}
	out, err := os.OpenFile(cfg.Output, openFlags, 0666)
	if err != nil {
		return fmt.Errorf("failed to open output '%s': %w", cfg.Output, err)
	}
	defer out.Close()

	totalSize, err := device.GetDeviceSize(cfg.Input)
	if err != nil {
		log.Printf("Warning: could not determine input size: %v\n", err)
		totalSize = 0
	}

	if cfg.Count > 0 {
		countSize := int64(cfg.Count) * int64(cfg.BlockSize)
		if totalSize == 0 || countSize < totalSize {
			totalSize = countSize
		}
	}

	if cfg.ShouldUseCopyOffload() {
		reporter := progress.NewReporter("Kernel Offload", cfg.ProgressChan)
		if cfg.Progress {
			reporter.Update(0, totalSize)
		}
		written, err := device.CopyFileRange(in, out, totalSize)
		if err == nil {
			if cfg.Progress {
				reporter.Finish(written)
			}
			return nil
		}
		if err != syscall.ENOSYS {
			return fmt.Errorf("kernel copy failed: %w", err)
		}
		log.Println("Kernel offload not supported, falling back to standard copy.")
	}

	if cfg.Skip > 0 {
		if _, err := in.Seek(cfg.Skip*int64(cfg.BlockSize), io.SeekStart); err != nil {
			return fmt.Errorf("failed to skip in input: %w", err)
		}
	}
	if cfg.Seek > 0 && !cfg.Append {
		if _, err := out.Seek(cfg.Seek*int64(cfg.BlockSize), io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek in output: %w", err)
		}
	}

	var reader io.Reader = in
	if cfg.Compression == "decompress" || (cfg.Compression == "auto" && strings.HasSuffix(cfg.Input, ".gz")) {
		if cfg.CompressionType == "gzip" {
			gr, err := gzip.NewReader(reader)
			if err != nil {
				return fmt.Errorf("failed to create gzip reader: %w", err)
			}
			defer gr.Close()
			reader = gr
			totalSize = 0
		}
	}

	bufReader := bufio.NewReaderSize(reader, cfg.BlockSize)
	return copyLoop(bufReader, out, out, cfg, totalSize)
}

func compressFile(inputPath, outputPath string, cfg *Config) error {
	in, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input for compression: %w", err)
	}
	defer in.Close()

	out, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create compressed output: %w", err)
	}
	defer out.Close()

	info, err := in.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info for compression: %w", err)
	}
	totalSize := info.Size()

	var writer io.Writer = out
	if cfg.CompressionType == "gzip" {
		gw, err := gzip.NewWriterLevel(writer, gzip.DefaultCompression)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer: %w", err)
		}
		defer gw.Close()
		writer = gw
	}

	reporter := progress.NewReporter("Compressing", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	buf := make([]byte, cfg.BlockSize)
	var written int64
	for {
		n, err := in.Read(buf)
		if n > 0 {
			if _, wErr := writer.Write(buf[:n]); wErr != nil {
				return fmt.Errorf("compression write error: %w", wErr)
			}
			written += int64(n)
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("compression read error: %w", err)
		}
	}

	if cfg.Progress {
		reporter.Finish(written)
	}
	return nil
}

func copyLoop(r io.Reader, w io.Writer, outFile *os.File, cfg *Config, totalSize int64) error {
	buf := make([]byte, cfg.BlockSize)
	var written int64 = 0
	blocksCopied := 0
	var reporter *progress.Reporter

	if cfg.Progress {
		stage := "Copying"
		if cfg.isRecursive {
			stage = "Copying Directory"
		}
		reporter = progress.NewReporter(stage, cfg.ProgressChan)
		if totalSize < 0 {
			totalSize = 0
		}
		reporter.Update(0, totalSize)
	}

	for {
		if cfg.Count > 0 && blocksCopied >= cfg.Count {
			break
		}

		n, err := r.Read(buf)
		if n > 0 {
			if cfg.Sparse && isAllZeros(buf[:n]) {
				if _, seekErr := outFile.Seek(int64(n), io.SeekCurrent); seekErr != nil {
					if _, wErr := w.Write(buf[:n]); wErr != nil {
						return fmt.Errorf("sparse seek and fallback write failed: %w", wErr)
					}
				}
			} else {
				if _, wErr := w.Write(buf[:n]); wErr != nil {
					return fmt.Errorf("write error: %w", wErr)
				}
			}
			written += int64(n)
			blocksCopied++
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			if cfg.SkipBadSectors {
				log.Printf("Read error at offset %d, skipping: %v\n", written, err)
				zeroBuf := make([]byte, cfg.BlockSize)
				if cfg.Sparse {
					if _, seekErr := outFile.Seek(int64(cfg.BlockSize), io.SeekCurrent); seekErr != nil {
						if _, wErr := w.Write(zeroBuf); wErr != nil {
							return fmt.Errorf("error writing zero block for bad sector: %w", wErr)
						}
					}
				} else {
					if _, wErr := w.Write(zeroBuf); wErr != nil {
						return fmt.Errorf("error writing zero block for bad sector: %w", wErr)
					}
				}
				written += int64(cfg.BlockSize)
				continue
			}
			return fmt.Errorf("read error: %w", err)
		}
	}

	// Ensure all data is written to disk
	if outFile != nil {
		outFile.Sync()
	}

	if f, ok := w.(io.Closer); ok {
		f.Close()
	}
	if cfg.Progress {
		reporter.Finish(written)
	}
	return nil
}

func copyDirectory(cfg *Config) error {
	log.Printf("Recursively copying directory %s to %s\n", cfg.Input, cfg.Output)
	return filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			return err
		}
		destPath := filepath.Join(cfg.Output, relPath)
		if d.IsDir() {
			info, statErr := os.Stat(path)
			if statErr != nil {
				return statErr
			}
			return os.MkdirAll(destPath, info.Mode())
		}
		fileCfg := *cfg
		fileCfg.Input = path
		fileCfg.Output = destPath
		fileCfg.isRecursive = false
		copyErr := copyFile(&fileCfg)
		if copyErr != nil {
			return fmt.Errorf("failed to copy file %s: %w", path, copyErr)
		}
		if cfg.PreserveAttributes {
			info, statErr := os.Stat(path)
			if statErr != nil {
				return statErr
			}
			os.Chmod(destPath, info.Mode())
		}
		return nil
	})
}

func doWipe(cfg *Config) error {
	out, err := os.OpenFile(cfg.Output, os.O_WRONLY, 0)
	if err != nil {
		return fmt.Errorf("failed to open output for wiping: %w", err)
	}
	defer out.Close()
	totalSize, err := device.GetDeviceSize(cfg.Output)
	if err != nil {
		log.Printf("Warning: could not determine wipe size: %v\n", err)
		totalSize = 0
	}
	buf := make([]byte, cfg.BlockSize)

	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Wipe", cfg.ProgressChan)
	}

	for pass := 1; pass <= cfg.WipePasses; pass++ {
		if cfg.Progress {
			stageName := fmt.Sprintf("Wiping (Pass %d/%d)", pass, cfg.WipePasses)
			reporter.SetStage(stageName)
			reporter.Update(0, totalSize)
		} else {
			fmt.Fprintf(os.Stderr, "Starting pass %d of %d...\n", pass, cfg.WipePasses)
		}

		if _, err := out.Seek(0, io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek to start for wipe pass %d: %w", pass, err)
		}
		var written int64 = 0
		for {
			if totalSize > 0 && written >= totalSize {
				break
			}
			if cfg.WipeMode == "random" {
				if _, err := rand.Read(buf); err != nil {
					return fmt.Errorf("failed to generate random data: %w", err)
				}
			} else {
				for i := range buf {
					buf[i] = 0
				}
			}
			n, err := out.Write(buf)
			if n > 0 {
				written += int64(n)
				if cfg.Progress {
					reporter.Update(written, totalSize)
				}
			}
			if err != nil {
				if totalSize > 0 && written >= totalSize {
					break
				}
				return fmt.Errorf("wipe write error: %w", err)
			}
		}
	}

	if cfg.Progress {
		reporter.Finish(totalSize)
	}
	return nil
}

func doVerify(cfg *Config) error {
	src, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open source '%s' for verification: %w", cfg.Input, err)
	}
	defer src.Close()

	if _, err := os.Stat(cfg.Output); os.IsNotExist(err) {
		return fmt.Errorf("verification failed: destination file '%s' does not exist", cfg.Output)
	}

	dst, err := os.Open(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to open destination '%s' for verification: %w", cfg.Output, err)
	}
	defer dst.Close()

	// Get source size - this is what we actually need to verify
	srcSize, err := device.GetDeviceSize(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to get source size for verification: %w", err)
	}

	// For destination, we need to check if it's a device or regular file
	dstStat, err := dst.Stat()
	if err != nil {
		return fmt.Errorf("failed to stat destination for verification: %w", err)
	}

	// If destination is a device, we only verify up to the source size
	// If destination is a regular file, it should match the source size exactly
	isDestDevice := (dstStat.Mode() & os.ModeDevice) != 0

	if !isDestDevice {
		// For regular files, sizes must match exactly
		dstSize, err := device.GetDeviceSize(cfg.Output)
		if err != nil {
			return fmt.Errorf("failed to get destination size for verification: %w", err)
		}

		if srcSize != dstSize {
			return fmt.Errorf("verification failed: file sizes differ (source: %d, destination: %d)",
				srcSize, dstSize)
		}
	} else {
		// For devices, just ensure the device is large enough to contain the source
		dstSize, err := device.GetDeviceSize(cfg.Output)
		if err != nil {
			return fmt.Errorf("failed to get destination size for verification: %w", err)
		}

		if srcSize > dstSize {
			return fmt.Errorf("verification failed: destination device too small (source: %d, destination: %d)",
				srcSize, dstSize)
		}
	}

	totalSize := srcSize
	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Verifying", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	srcBuf := make([]byte, cfg.BlockSize)
	dstBuf := make([]byte, cfg.BlockSize)
	var verified int64

	for {
		n, srcErr := src.Read(srcBuf)
		if srcErr != nil && srcErr != io.EOF {
			return fmt.Errorf("error reading source during verification: %w", srcErr)
		}

		if n == 0 {
			break
		}

		_, dstErr := io.ReadFull(dst, dstBuf[:n])
		if dstErr != nil {
			if dstErr == io.ErrUnexpectedEOF || dstErr == io.EOF {
				return fmt.Errorf("verification failed: destination is shorter than source at offset %d", verified)
			}
			return fmt.Errorf("error reading destination during verification: %w", dstErr)
		}

		if !bytes.Equal(srcBuf[:n], dstBuf[:n]) {
			return fmt.Errorf("verification failed: data mismatch at offset %d", verified)
		}

		verified += int64(n)
		if cfg.Progress {
			reporter.Update(verified, totalSize)
		}

		if srcErr == io.EOF {
			break
		}
	}

	if cfg.Progress {
		reporter.Finish(verified)
	} else {
		fmt.Printf("Verification successful: %s verified.\n", progress.HumanizeBytes(uint64(verified)))
	}

	return nil
}

func isAllZeros(data []byte) bool {
	for _, b := range data {
		if b != 0 {
			return false
		}
	}
	return true
}
