package ui

import (
	"strings"
	"testing"
	"time"
)

func TestProgressManager_Creation(t *testing.T) {
	// Test progress manager creation with different stage configurations
	tests := []struct {
		name       string
		stageNames []string
		expected   int
	}{
		{"Single stage", []string{"Copying"}, 1},
		{"Two stages", []string{"Copying", "Verifying"}, 2},
		{"Three stages", []string{"Copying", "Verifying", "Compressing"}, 3},
		{"Empty stages", []string{}, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pm := NewProgressManager(tt.stageNames)
			if pm == nil {
				t.Fatal("ProgressManager should not be nil")
			}

			pm.mu.RLock()
			stageCount := len(pm.stages)
			pm.mu.RUnlock()

			if stageCount != tt.expected {
				t.Errorf("Expected %d stages, got %d", tt.expected, stageCount)
			}
		})
	}
}

func TestProgressManager_StageUpdates(t *testing.T) {
	// Test stage update functionality
	stageNames := []string{"Copying", "Verifying"}
	pm := NewProgressManager(stageNames)

	// Test initial state
	pm.mu.RLock()
	if pm.currentStage != 0 {
		t.Errorf("Expected initial stage 0, got %d", pm.currentStage)
	}
	pm.mu.RUnlock()

	// Test stage update
	pm.UpdateStage(500, 1000, 10.5, "ETA: 30s")

	pm.mu.RLock()
	stage := pm.stages[0]
	pm.mu.RUnlock()

	if stage.Current != 500 {
		t.Errorf("Expected current bytes 500, got %d", stage.Current)
	}
	if stage.Total != 1000 {
		t.Errorf("Expected total bytes 1000, got %d", stage.Total)
	}
	if stage.Speed != 10.5 {
		t.Errorf("Expected speed 10.5, got %f", stage.Speed)
	}
	if stage.ETA != "ETA: 30s" {
		t.Errorf("Expected ETA 'ETA: 30s', got %s", stage.ETA)
	}
}

func TestProgressManager_StageCompletion(t *testing.T) {
	// Test stage completion and progression
	stageNames := []string{"Copying", "Verifying"}
	pm := NewProgressManager(stageNames)

	// Complete first stage
	pm.CompleteStage()

	pm.mu.RLock()
	firstStage := pm.stages[0]
	currentStageIndex := pm.currentStage
	pm.mu.RUnlock()

	if !firstStage.Completed {
		t.Error("First stage should be marked as completed")
	}
	if firstStage.Current != firstStage.Total {
		t.Error("Completed stage current should equal total")
	}
	if currentStageIndex != 1 {
		t.Errorf("Expected current stage 1, got %d", currentStageIndex)
	}
}

func TestProgressManager_OverallProgress(t *testing.T) {
	// Test overall progress calculation
	stageNames := []string{"Stage1", "Stage2", "Stage3"}
	pm := NewProgressManager(stageNames)

	// Set up stages with different progress
	pm.mu.Lock()
	pm.stages[0].Current = 1000
	pm.stages[0].Total = 1000
	pm.stages[0].Completed = true

	pm.stages[1].Current = 500
	pm.stages[1].Total = 1000
	pm.stages[1].Completed = false
	pm.currentStage = 1 // Set current stage to stage 1

	pm.stages[2].Current = 0
	pm.stages[2].Total = 1000
	pm.stages[2].Completed = false
	pm.mu.Unlock()

	overall := pm.calculateOverallProgress()

	// Expected calculation:
	// Stage 0 (completed): (1/3) * 100 = 33.33%
	// Stage 1 (current, 50% done): (1/3) * 50 = 16.67%
	// Stage 2 (not current, not completed): 0%
	// Total: 33.33 + 16.67 = 50%
	expected := 50.0
	// Use a small tolerance for floating point comparison
	tolerance := 0.1
	if overall < expected-tolerance || overall > expected+tolerance {
		t.Errorf("Expected overall progress %.1f%%, got %.1f%%", expected, overall)
	}
}

func TestProgressManager_ErrorHandling(t *testing.T) {
	// Test error handling
	stageNames := []string{"Copying"}
	pm := NewProgressManager(stageNames)

	errorMsg := "Test error message"
	pm.Error(errorMsg)

	// Since we can't easily test the channel communication without setting up
	// the full UI infrastructure, we just test that the method doesn't panic
	// and that the error message is properly formatted
	if !strings.Contains(errorMsg, "Test error") {
		t.Error("Error message should contain the expected text")
	}
}

func TestProgressManager_Finish(t *testing.T) {
	// Test finish functionality
	stageNames := []string{"Copying", "Verifying"}
	pm := NewProgressManager(stageNames)

	summary := "Operation completed successfully"
	pm.Finish(summary)

	// Test that finish doesn't panic and handles the summary correctly
	if !strings.Contains(summary, "completed") {
		t.Error("Summary should contain completion message")
	}
}

func TestProgressBarCreation(t *testing.T) {
	// Test progress bar visual creation
	app := &App{} // Create minimal app for testing

	tests := []struct {
		name     string
		label    string
		percent  float64
		color    string
		expected bool // Whether the result should contain expected elements
	}{
		{"Zero progress", "Test", 0.0, "[green]", true},
		{"Half progress", "Test", 50.0, "[yellow]", true},
		{"Full progress", "Test", 100.0, "[green]", true},
		{"Over 100%", "Test", 150.0, "[green]", true}, // Should cap at 100%
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := app.createProgressBar(tt.label, tt.percent, tt.color)

			if !strings.Contains(result, tt.label) {
				t.Errorf("Progress bar should contain label %s", tt.label)
			}

			// Check that percentage is properly formatted
			if !strings.Contains(result, "%.1f%%") && !strings.Contains(result, "%") {
				// The result should contain a percentage, but since we're testing the format
				// we need to check the actual implementation
				barWidth := 40
				filled := int(tt.percent * float64(barWidth) / 100.0)
				if filled < 0 {
					filled = 0
				}
				if filled > barWidth {
					filled = barWidth
				}

				expectedPercent := tt.percent
				if expectedPercent > 100.0 {
					expectedPercent = 100.0
				}

				// Verify the calculation is correct
				if filled > barWidth {
					t.Error("Filled portion should not exceed bar width")
				}
				if filled < 0 {
					t.Error("Filled portion should not be negative")
				}
			}
		})
	}
}

func TestProgressUpdate_Structure(t *testing.T) {
	// Test ProgressUpdate structure and field validation
	update := ProgressUpdate{
		StageIndex:     0,
		StageName:      "Copying",
		StagePercent:   50.0,
		StageSpeed:     10.5,
		StageETA:       "ETA: 30s",
		OverallPercent: 25.0,
		IsComplete:     false,
		Error:          "",
		Summary:        "",
	}

	// Test field values
	if update.StageIndex != 0 {
		t.Errorf("Expected StageIndex 0, got %d", update.StageIndex)
	}
	if update.StageName != "Copying" {
		t.Errorf("Expected StageName 'Copying', got %s", update.StageName)
	}
	if update.StagePercent != 50.0 {
		t.Errorf("Expected StagePercent 50.0, got %f", update.StagePercent)
	}
	if update.StageSpeed != 10.5 {
		t.Errorf("Expected StageSpeed 10.5, got %f", update.StageSpeed)
	}
	if update.IsComplete {
		t.Error("Expected IsComplete false")
	}
	if update.Error != "" {
		t.Errorf("Expected empty Error, got %s", update.Error)
	}
}

func TestProgressStage_Structure(t *testing.T) {
	// Test ProgressStage structure
	stage := ProgressStage{
		Name:      "Copying",
		Current:   500,
		Total:     1000,
		Speed:     10.5,
		ETA:       "ETA: 30s",
		Completed: false,
		StartTime: time.Now(),
	}

	// Test field values
	if stage.Name != "Copying" {
		t.Errorf("Expected Name 'Copying', got %s", stage.Name)
	}
	if stage.Current != 500 {
		t.Errorf("Expected Current 500, got %d", stage.Current)
	}
	if stage.Total != 1000 {
		t.Errorf("Expected Total 1000, got %d", stage.Total)
	}
	if stage.Speed != 10.5 {
		t.Errorf("Expected Speed 10.5, got %f", stage.Speed)
	}
	if stage.Completed {
		t.Error("Expected Completed false")
	}
	if stage.StartTime.IsZero() {
		t.Error("StartTime should not be zero")
	}
}

func TestBridgeProgress_StageMapping(t *testing.T) {
	// Test stage mapping logic for bridge progress
	stageMap := map[string]int{
		"Copying":     0,
		"Verifying":   1,
		"Compressing": 2,
	}

	tests := []struct {
		stage    string
		expected int
		exists   bool
	}{
		{"Copying", 0, true},
		{"Verifying", 1, true},
		{"Compressing", 2, true},
		{"Unknown", 0, false},
	}

	for _, tt := range tests {
		t.Run(tt.stage, func(t *testing.T) {
			index, exists := stageMap[tt.stage]
			if exists != tt.exists {
				t.Errorf("Expected exists %v, got %v", tt.exists, exists)
			}
			if exists && index != tt.expected {
				t.Errorf("Expected index %d, got %d", tt.expected, index)
			}
		})
	}
}
