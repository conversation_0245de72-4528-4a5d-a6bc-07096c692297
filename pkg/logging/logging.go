package logging

import (
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
)

// ProgressWriter is a thread-safe io.Writer that allows log messages
// to be printed above a sticky progress bar line.
type ProgressWriter struct {
	mu               sync.Mutex
	lastProgressLine string
}

// Write implements the io.Writer interface.
// It prints the given message above the last known progress line.
func (w *ProgressWriter) Write(p []byte) (n int, err error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	message := strings.TrimSpace(string(p))
	if message == "" {
		return len(p), nil
	}

	// Clear the current line, print the log message, then restore progress if any
	if w.lastProgressLine != "" {
		fmt.Fprintf(os.Stderr, "\r\033[K%s\n%s", message, w.lastProgressLine)
	} else {
		fmt.Fprintf(os.Stderr, "%s\n", message)
	}

	return len(p), nil
}

// SetProgressLine updates the writer with the latest progress bar string.
func (w *ProgressWriter) SetProgressLine(line string) {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.lastProgressLine = line
}

// ClearProgressLine clears the stored progress line to prevent it from being restored.
func (w *ProgressWriter) ClearProgressLine() {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.lastProgressLine = ""
}

// LogWriter is the global instance of our custom writer.
var LogWriter = &ProgressWriter{}

// Init sets up the standard log package to use our custom writer.
func Init() {
	// Redirect all standard log output to our custom writer.
	log.SetOutput(LogWriter)
	// Remove standard log prefixes (like date/time) for cleaner output.
	log.SetFlags(0)
}
